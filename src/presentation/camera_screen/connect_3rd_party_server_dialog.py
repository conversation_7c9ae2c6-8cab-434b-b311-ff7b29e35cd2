from PySide6.QtWidgets import QLabel, QComboBox, QLineEdit, QPushButton, QVBoxLayout, QWidget, QDialog, QHBoxLayout, QCheckBox
from PySide6.QtCore import Qt, QPropertyAnimation, QRect, QSize
from PySide6.QtGui import QIcon, QMovie
from src.common.widget.notifications.notify import Notifications
from src.common.controller.controller_manager import controller_manager
from src.common.model.group_model import Group
from src.common.controller.main_controller import main_controller
from src.presentation.device_management_screen.widget.list_custom_widgets import InputWithTitle
from src.utils.config import Config
from src.utils.log_utils import logger
from src.styles.style import Style
import re
from ipaddress import ip_address

# create dialog connect 3rd party server: combobox server type, server ip, port, username, password
class Connect3rdPartyServerDialog(QDialog):
    def __init__(self, parent=None, server=""):
        super().__init__(parent)
        self.setModal(True)
        self.setWindowFlag(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedWidth(700)
        self.setFixedHeight(600)

        self.server = server
        self.shake_animation = None
        logger.debug(f"server: {type(server)}")

        # Tạo central_widget bọc main_layout
        self.central_widget = QWidget(self)
        self.central_widget.setObjectName("Connect3rdPartyDialogCentralBox")
        main_layout = QHBoxLayout(self.central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(0)

        # Left: Logo/Image
        self.left_widget = QWidget()
        self.left_layout = QVBoxLayout(self.left_widget)
        self.left_layout.setContentsMargins(0, 0, 0, 0)
        self.left_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.logo_label = QLabel()
        self.logo_label.setFixedSize(350, 580)
        self.logo_label.setStyleSheet("background-color: #101828; border-radius: 16px;")
        self.logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.movie = QMovie(main_controller.get_theme_attribute("Image", "logo_video"))
        self.movie.setScaledSize(QSize(350, 580))
        self.logo_label.setMovie(self.movie)
        self.movie.start()
        self.left_layout.addWidget(self.logo_label)
        main_layout.addWidget(self.left_widget)

        # Right: Form nhập thông tin
        self.right_widget = QWidget()
        self.right_layout = QVBoxLayout(self.right_widget)
        self.right_layout.setContentsMargins(24, 24, 24, 24)
        self.right_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.form_widget = self.create_form_widget()
        self.right_layout.addWidget(self.form_widget)
        main_layout.addWidget(self.right_widget)

        # Đặt central_widget vào layout ngoài cùng của dialog
        outer_layout = QVBoxLayout(self)
        outer_layout.addWidget(self.central_widget)
        outer_layout.setContentsMargins(0, 0, 0, 0)
        outer_layout.setSpacing(0)

        bg = main_controller.get_theme_attribute("Color", "login_dialog_background")
        # Set style cho central_widget (border + background)
        self.central_widget.setStyleSheet(f"""
            #Connect3rdPartyDialogCentralBox {{
                background-color: {bg};
            }}
        """)

        self.set_dynamic_style()
        self.server_type_combobox_changed()

    def create_form_widget(self):
        widget = QWidget()

        self.title = QLabel(self.tr(f'Connect to {self.server.name}'))
        self.title.setAlignment(Qt.AlignCenter)

        # Server Type
        self.server_type_widget = InputWithTitle(title=self.tr("Server Type"), is_require_field=True)
        self.server_type_combobox = QComboBox()
        self.server_type_combobox.addItems(["Avigilon", "Milestone"])
        self.server_type_combobox.currentIndexChanged.connect(self.server_type_combobox_changed)
        # Replace line edit with combobox
        self.server_type_widget.layout.removeWidget(self.server_type_widget.line_edit)
        self.server_type_widget.line_edit.hide()
        self.server_type_widget.layout.addWidget(self.server_type_combobox)

        # Server Name
        self.server_name_widget = InputWithTitle(title=self.tr("Server Name"), text_placeholder=self.tr("Enter server name"), is_require_field=True)

        # Server IP with regex validation
        self.server_ip_widget = InputWithTitle(title=self.tr("Server IP"), text_placeholder=self.tr("Server IP: *************"), is_require_field=True)
        self.server_ip_widget.line_edit.textChanged.connect(self.validate_server_ip)

        # Port
        self.port_widget = InputWithTitle(title=self.tr("Port"), text_placeholder=self.tr("Enter port"), is_require_field=True)

        # TLS Checkbox
        self.tls_widget = QWidget()
        tls_layout = QHBoxLayout(self.tls_widget)
        tls_layout.setContentsMargins(0, 0, 0, 0)
        self.tls_checkbox = QCheckBox(self.tr("Use TLS/SSL"))
        tls_layout.addWidget(self.tls_checkbox)
        tls_layout.addStretch()

        # Username
        self.username_widget = InputWithTitle(title=self.tr("Username"), text_placeholder=self.tr("Enter username"), is_require_field=True)

        # Password
        self.password_widget = InputWithTitle(title=self.tr("Password"), text_placeholder=self.tr("Enter password"), is_password_line=True, is_require_field=True)

        # Error label
        self.error_label = QLabel("")
        self.error_label.setStyleSheet(f"color: {Style.PrimaryColor.primary}; font-weight: 400")
        self.error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Control buttons
        control_layout = QHBoxLayout()
        self.connect_button = QPushButton(self.tr("Connect"))
        self.connect_button.setFixedHeight(50)
        self.connect_button.clicked.connect(self.save_update)

        self.cancel_button = QPushButton(self.tr("Cancel"))
        self.cancel_button.setFixedHeight(50)
        self.cancel_button.clicked.connect(self.cancel_clicked)

        control_layout.addWidget(self.connect_button)
        control_layout.addWidget(self.cancel_button)

        # Layout
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.title, Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.server_type_widget)
        layout.addWidget(self.server_name_widget)
        layout.addWidget(self.server_ip_widget)
        layout.addWidget(self.port_widget)
        layout.addWidget(self.tls_widget)
        layout.addWidget(self.username_widget)
        layout.addWidget(self.password_widget)
        layout.addWidget(self.error_label)
        layout.addLayout(control_layout)

        return widget

    def server_type_combobox_changed(self):
        if self.server_type_combobox.currentText() == "Avigilon":
            self.port_widget.line_edit.setText("8443")
        elif self.server_type_combobox.currentText() == "Milestone":
            self.port_widget.line_edit.setText("8080")

    def validate_server_ip(self, text):
        """Validate server IP with regex patterns"""
        # IP address pattern
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        # Domain name pattern
        domain_pattern = r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'
        # URL pattern
        url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'

        if re.match(ip_pattern, text) or re.match(domain_pattern, text) or re.match(url_pattern, text):
            self.server_ip_widget.label_warning.hide()
            return True
        elif text:  # Only show warning if text is not empty
            self.server_ip_widget.label_warning.setText(self.tr("Invalid IP address, domain, or URL format"))
            self.server_ip_widget.label_warning.show()
            return False
        else:
            self.server_ip_widget.label_warning.hide()
            return False

    def is_valid_ip(self, ip):
        """Check if the given string is a valid IP address using ipaddress module"""
        try:
            ip_address(ip)
            return True
        except ValueError:
            return False

    def cancel_clicked(self):
        self.close()

    def start_shake_animation(self, widget: QWidget):
        if self.shake_animation:
            start_rect = widget.geometry()
            shake_rect = QRect(start_rect.x() - 5, start_rect.y(),
                             start_rect.width(), start_rect.height())
            self.shake_animation.setStartValue(start_rect)
            self.shake_animation.setEndValue(shake_rect)
            self.shake_animation.start()
            # set old geometry after animation end
            self.shake_animation.finished.connect(
                lambda: widget.setGeometry(start_rect))

    def save_update(self):
        # check if shake animation is running -> return
        if self.shake_animation and self.shake_animation.state() == QPropertyAnimation.State.Running:
            return

        # Get form data
        server_name = self.server_name_widget.line_edit.text()
        server_ip = self.server_ip_widget.line_edit.text()
        port = self.port_widget.line_edit.text()
        username = self.username_widget.line_edit.text()
        password = self.password_widget.line_edit.text()
        use_tls = self.tls_checkbox.isChecked()

        if self.error_label:
            self.error_label.setText("")
            # add effect shake text
            self.shake_animation = QPropertyAnimation(self.error_label, b"geometry")
            self.shake_animation.setDuration(100)
            self.shake_animation.setLoopCount(5)

        # Validation
        if server_name == "":
            self.error_label.setText(self.tr("Please enter server name."))
            self.start_shake_animation(self.error_label)
            return
        elif server_ip == "":
            self.error_label.setText(self.tr("Please enter server IP."))
            self.start_shake_animation(self.error_label)
            return
        elif not self.validate_server_ip(server_ip):
            self.error_label.setText(self.tr("Please enter a valid server IP, domain, or URL."))
            self.start_shake_animation(self.error_label)
            return
        elif port == "":
            self.error_label.setText(self.tr("Please enter port."))
            self.start_shake_animation(self.error_label)
            return
        elif not port.isdigit() or not (1 <= int(port) <= 65535):
            self.error_label.setText(self.tr("Please enter a valid port number (1-65535)."))
            self.start_shake_animation(self.error_label)
            return
        elif username == "":
            self.error_label.setText(self.tr("Please enter username."))
            self.start_shake_animation(self.error_label)
            return
        elif password == "":
            self.error_label.setText(self.tr("Please enter password."))
            self.start_shake_animation(self.error_label)
            return

        # Determine server type
        self.server_type = None
        if self.server_type_combobox.currentText() == "Avigilon":
            self.server_type = "AVIGILON"
        elif self.server_type_combobox.currentText() == "Milestone":
            self.server_type = "MILESTONE"

        logger.debug(f"server_name: {server_name}, server_ip: {server_ip}, port: {port}, username: {username}, password: {password}, client_name: {server_name}, server_type: {self.server_type}, use_tls: {use_tls}")

        # Create group with TLS support
        new_group = Group(
            name=server_name,
            type="THIRD_PARTY_SERVER",
            serverUsername=username,
            serverPassword=password,
            serverIp=server_ip,
            serverPort=int(port),
            clientName=server_name,
            serverType=self.server_type
        )

        # Add TLS information to group if needed (you may need to extend Group model)
        # new_group.useTLS = use_tls

        controller = controller_manager.get_controller(server_ip=self.server.name)
        controller.create_camera_group(parent=self, data=new_group, is_insert_group=True)
        self.accept()

    def set_dynamic_style(self):
        # Style giống btn_connect trong login_dialog.py
        self.connect_button.setStyleSheet(f'''
            QPushButton {{
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
            }}
            QPushButton {{
                background-color: {main_controller.get_theme_attribute("Color", "primary")};
                color: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                font-size: 14px;
                border: {main_controller.get_theme_attribute("Color", "text_same_bg")};
                border-radius: 10px;
                padding: 1px;
            }}
            QPushButton::hover {{
                background-color: {main_controller.get_theme_attribute("Color", "text_pressed")};
                font-size: 14px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "text_same_bg")};
                border-radius: 10px;
                padding: 5px;
            }}
            QPushButton::pressed {{
                background-color: {Style.PrimaryColor.primary_pressed};
            }}
        ''')

        self.cancel_button.setStyleSheet(f'''
            QPushButton {{
                color: #ffffff;
            }}
            QPushButton {{
                background-color: #5c5c5c;
                color: #ffffff;
                font-size: 14px;
                border: #5c5c5c;
                border-radius: 10px;
                padding: 1px;
            }}
            QPushButton::hover {{
                background-color: #7a7a7a;
                font-size: 14px;
                border: 1px solid #ffffff;
                border-radius: 10px;
                padding: 5px;
            }}
            QPushButton::pressed {{
                background-color: #7a7a7a;
            }}
        ''')

        self.title.setStyleSheet(f'''
            QLabel {{
                background-color: transparent;
                color: {main_controller.get_theme_attribute("Color", "add_server_main_title")};
                font-size: 24px;
                font-weight: 200;
            }}
        ''')