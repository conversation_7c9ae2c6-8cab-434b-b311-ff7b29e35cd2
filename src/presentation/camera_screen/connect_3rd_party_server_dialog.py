from PySide6.QtWidgets import QLabel, QComboBox, QLineEdit, QPushButton, QVBoxLayout, QWidget
from src.common.widget.notifications.notify import Notifications
from src.common.controller.controller_manager import controller_manager
from src.common.model.group_model import Group
from src.common.widget.dialogs.base_dialog import FooterType, NewBaseDialog
from src.utils.config import Config
from src.utils.log_utils import logger
from src.styles.style import Style
# create dialog connect 3rd party server: combobox server type, server ip, port, username, password
class Connect3rdPartyServerDialog(NewBaseDialog):
    def __init__(self, parent=None, server=""):
        self.server = server
        logger.debug(f"server: {type(server)}")
        title = self.tr(f"Server: {server.name}")
        # self.setWindowTitle(f"Connect 3rd Party Server: {server.name}")
        # Avigilon, Milestone
        self.server_name = QLabel("Server Name (*)")
        self.server_type_label = QLabel("Server Type")
        self.server_type_combobox = QComboBox()
        self.server_type_combobox.addItems(["Avigilon", "Milestone"])
        # signal self.server_type_combobox
        self.server_type_combobox.currentIndexChanged.connect(self.server_type_combobox_changed)
        self.server_name_edit = QLineEdit()
        self.server_ip_label = QLabel("Server IP (*)")
        self.server_ip_lineedit = QLineEdit()
        self.port_label = QLabel("Port (*)")
        self.port_lineedit = QLineEdit()
        self.username_label = QLabel("Username (*)")
        self.username_lineedit = QLineEdit()
        self.password_label = QLabel("Password (*)")
        self.password_lineedit = QLineEdit()
        self.connect_button = QPushButton("Connect")
        # self.cancel_button = QPushButton("Cancel")
        self.layout = QVBoxLayout()
        self.layout.addWidget(self.server_type_label)
        self.layout.addWidget(self.server_type_combobox)
        self.layout.addWidget(self.server_name)
        self.layout.addWidget(self.server_name_edit)
        self.layout.addWidget(self.server_ip_label)
        self.layout.addWidget(self.server_ip_lineedit)
        self.layout.addWidget(self.port_label)
        self.layout.addWidget(self.port_lineedit)
        self.layout.addWidget(self.username_label)
        self.layout.addWidget(self.username_lineedit)
        self.layout.addWidget(self.password_label)
        self.layout.addWidget(self.password_lineedit)
        # self.button_layout = QHBoxLayout()
        # self.button_layout.addWidget(self.connect_button)
        # self.button_layout.addWidget(self.cancel_button)
        # self.layout.addLayout(self.button_layout)
        self.widget_main = QWidget()
        self.widget_main.setLayout(self.layout)
        super().__init__(parent, title=title, content_widget=self.widget_main, width_dialog=Config.WIDTH_DIALOG_MINI, min_height_dialog=400, footer_type=FooterType.CONNECT)
        self.setObjectName("Connect3rdPartyServerDialog")
        self.save_update_signal.connect(self.save_update)
        self.server_type_combobox_changed()

    def server_type_combobox_changed(self):
        if self.server_type_combobox.currentText() == "Avigilon":
            self.port_lineedit.setText("8443")
        elif self.server_type_combobox.currentText() == "Milestone":
            self.port_lineedit.setText("8080")

    def save_update(self):
        # check validate
        if self.server_name_edit.text() == "" or self.server_ip_lineedit.text() == "" or self.port_lineedit.text() == "" or self.username_lineedit.text() == "" or self.password_lineedit.text() == "":
            # Notifications(parent=main_controller.list_parent['HomeScreen'], title=self.tr('Please select Camera.'),icon=Style.PrimaryImage.info_result)
            need_fill = self.tr('Please fill in the following fields:\n')
            if self.server_name_edit.text() == "":
                need_fill += self.tr('Server Name\n')
            if self.server_ip_lineedit.text() == "":
                need_fill += self.tr('Server IP\n')
            if self.port_lineedit.text() == "":
                need_fill += self.tr('Port\n')
            if self.username_lineedit.text() == "":
                need_fill += self.tr('Username\n')
            if self.password_lineedit.text() == "":
                need_fill += self.tr('Password\n')
            Notifications(parent=self, title=need_fill,icon=Style.PrimaryImage.fail_result)
            return


        self.server_type = None
        if self.server_type_combobox.currentText() == "Avigilon":
            self.server_type = "AVIGILON"
        elif self.server_type_combobox.currentText() == "Milestone":
            self.server_type = "MILESTONE"

        server_name = self.server_name_edit.text()
        server_ip = self.server_ip_lineedit.text()
        port = self.port_lineedit.text()
        username = self.username_lineedit.text()
        password = self.password_lineedit.text()

        logger.debug(f"server_name: {server_name}, server_ip: {server_ip}, port: {port}, username: {username}, password: {password}, client_name: {server_name}, server_type: {self.server_type}")

        new_group = Group(name=server_name, type="THIRD_PARTY_SERVER", serverUsername=username, serverPassword=password, serverIp=server_ip, serverPort=port, clientName=server_name, serverType=self.server_type)
        controller = controller_manager.get_controller(server_ip=self.server.name)
        controller.create_camera_group(parent=self, data=new_group, is_insert_group=True)
        self.accept()
        